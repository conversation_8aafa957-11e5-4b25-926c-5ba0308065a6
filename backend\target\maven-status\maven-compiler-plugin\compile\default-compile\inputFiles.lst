C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\HealthGoalController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\UserMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\FileServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\utils\AuthUtil.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\MealRecordMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\EmailCodeLoginRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\config\FileUploadConfig.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\MealRecordServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\utils\JwtUtil.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\FoodController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\FileController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\DietaryPreference.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\DietaryPreferenceController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\UserServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\DietaryPreferenceServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\MealRecord.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\CoreFeatureServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\MealRecordController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\MealRecordResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\Admin.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\EmailService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\AdminResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\AdminMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\HealthGoalResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\FoodServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\DietaryPreferenceService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\ChinaFood.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\UserService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\common\Result.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\UserStatsResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\request\DietaryPreferenceRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\UserController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\CoreFeatureMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\CoreFeatureRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\FileService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\EmailVerification.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\request\UpdateProfileRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\FoodSearchResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\interceptor\JwtInterceptor.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\ChinaFoodCategoryMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\CoreFeatureController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\HealthGoalMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\AdminService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\DietaryPreferenceMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\EmailVerificationService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\NutritionResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\controller\AdminController.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\LoginRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\request\HealthGoalRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\UserResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\HealthGoalService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\MealRecordService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\DietaryPreferenceResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\FoodService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\ChinaFoodMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\EmailServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\User.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\ChinaFoodCategory.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\MealItemMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\AdminServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\HealthGoalServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\mapper\EmailVerificationMapper.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\config\WebConfig.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\AdminLoginRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\config\SecurityConfig.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\AdminEmailCodeLoginRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\HealthGoal.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\CoreFeatureService.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\PageResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\MealItem.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\RegisterRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\MealRecordRequest.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\CoreFeatureResponse.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\MealsApplication.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\service\impl\EmailVerificationServiceImpl.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\entity\CoreFeature.java
C:\Users\<USER>\Desktop\meals\backend\src\main\java\com\example\meals\dto\request\ResetPasswordRequest.java
