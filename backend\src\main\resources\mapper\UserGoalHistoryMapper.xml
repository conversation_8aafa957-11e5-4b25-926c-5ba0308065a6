<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.UserGoalHistoryMapper">

    <!-- 结果映射 -->
    <resultMap id="UserGoalHistoryResultMap" type="com.example.meals.entity.UserGoalHistory">
        <id property="id" column="id"/>
        <result property="goalId" column="goal_id"/>
        <result property="userId" column="user_id"/>
        <result property="recordDate" column="record_date"/>
        <result property="value" column="value"/>
        <result property="progress" column="progress"/>
        <result property="notes" column="notes"/>
        <result property="createdAt" column="created_at"/>
    </resultMap>

    <!-- 根据目标ID查询历史记录 -->
    <select id="findByGoalId" resultMap="UserGoalHistoryResultMap">
        SELECT * FROM user_goal_history 
        WHERE goal_id = #{goalId} AND user_id = #{userId} 
        ORDER BY record_date DESC 
        LIMIT #{limit}
    </select>

    <!-- 根据目标ID和日期范围查询历史记录 -->
    <select id="findByGoalIdAndDateRange" resultMap="UserGoalHistoryResultMap">
        SELECT * FROM user_goal_history 
        WHERE goal_id = #{goalId} AND user_id = #{userId} 
        AND record_date >= #{startDate} AND record_date <= #{endDate} 
        ORDER BY record_date DESC
    </select>

    <!-- 根据用户ID查询最近的历史记录 -->
    <select id="findRecentByUserId" resultMap="UserGoalHistoryResultMap">
        SELECT * FROM user_goal_history 
        WHERE user_id = #{userId} 
        ORDER BY record_date DESC 
        LIMIT #{limit}
    </select>

    <!-- 插入历史记录 -->
    <insert id="insert" parameterType="com.example.meals.entity.UserGoalHistory" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_goal_history (
            goal_id, user_id, record_date, value, progress, notes
        ) VALUES (
            #{goalId}, #{userId}, #{recordDate}, #{value}, #{progress}, #{notes}
        )
    </insert>

    <!-- 更新或插入历史记录（如果当天已有记录则更新，否则插入） -->
    <insert id="insertOrUpdate" parameterType="com.example.meals.entity.UserGoalHistory">
        INSERT INTO user_goal_history (
            goal_id, user_id, record_date, value, progress, notes
        ) VALUES (
            #{goalId}, #{userId}, #{recordDate}, #{value}, #{progress}, #{notes}
        ) ON DUPLICATE KEY UPDATE 
            value = VALUES(value),
            progress = VALUES(progress),
            notes = VALUES(notes)
    </insert>

    <!-- 根据目标ID和日期查询历史记录 -->
    <select id="findByGoalIdAndDate" resultMap="UserGoalHistoryResultMap">
        SELECT * FROM user_goal_history 
        WHERE goal_id = #{goalId} AND user_id = #{userId} AND record_date = #{recordDate}
    </select>

    <!-- 删除目标的所有历史记录 -->
    <delete id="deleteByGoalId">
        DELETE FROM user_goal_history 
        WHERE goal_id = #{goalId} AND user_id = #{userId}
    </delete>

    <!-- 删除指定日期的历史记录 -->
    <delete id="deleteByGoalIdAndDate">
        DELETE FROM user_goal_history 
        WHERE goal_id = #{goalId} AND user_id = #{userId} AND record_date = #{recordDate}
    </delete>

    <!-- 统计目标的历史记录数量 -->
    <select id="countByGoalId" resultType="int">
        SELECT COUNT(*) FROM user_goal_history 
        WHERE goal_id = #{goalId} AND user_id = #{userId}
    </select>

    <!-- 获取用户连续记录天数（简化版本，实际可能需要更复杂的逻辑） -->
    <select id="getStreakDays" resultType="int">
        SELECT COUNT(DISTINCT record_date) FROM user_goal_history 
        WHERE user_id = #{userId} 
        AND record_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
    </select>

</mapper>
